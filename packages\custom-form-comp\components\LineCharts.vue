<script setup lang='ts'>
defineOptions({ name: 'LineCharts' })
import * as echarts from 'echarts'
const props = withDefaults(
  defineProps<{
    width?: string
    height?: string
    left?: string | number
    modelValue: any
    title?: any
    legend?: any
    xAxis?: any
    yAxis?: any
    grid?: any
    series?: any
  }>(),
  {
    left: 'left',
    width: '100%',
    height: '100%',
    title: {
      text: '折线图'
    },
    legend: {
      show: false
    },
    xAxis: {
      axisLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          dashOffset: 10,
          color: '#dddddd'
        }
      },
      axisLabel: {
        color: '#666666'
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          dashOffset: 10
        }
      },
      type: 'category',
      boundaryGap: false,
    },
    yAxis: [
      {
        type: 'value',
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            dashOffset: 10
          }
        }
      }
    ],
    grid: {
      left: '4%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    series: [
      {
        type: 'line',
        stack: 'Total',
        color: '#01b863',
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(1,184,99,0.3)'
            },
            {
              offset: 1,
              color: 'rgba(1,184,99,0.3)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
      },
      {
        type: 'line',
        stack: 'Total',
        color: '#0e9cf5',
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: 'rgba(14,156,255,0.3)'
            },
            {
              offset: 1,
              color: 'rgba(14,156,255,0.3)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
      }
    ]
  }
)

const getOptions = (xData: any[], yData: any[]): any => {
  const series:any = []
  const legendData: any = []
  if (!props.series || props.series.length === 0) return {}
  if (props.series && props.series.length > 0){
    props.series?.forEach((item, index) => {
      series.push({
        ...item,
        name: yData[index]?.name,
        data: yData[index]?.value
      })
      if (yData[index]?.name) legendData.push(yData[index]?.name)
    })
  }

  return {
    title: props.title,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      ...props.legend,
      data: legendData
    },
    grid: props.grid,
    xAxis: {
      ...props.xAxis,
      data: xData
    },
    yAxis: props.yAxis,
    series: series
  }
}
watch(
  () => props.modelValue,
  (val: any) => {
    initFun(val)
  }
)
const myChartsDom = ref()
const initFun = (val: any) => {
  nextTick(() => {
    const myChart = echarts.init(myChartsDom.value)
    myChart.setOption(getOptions(val?.xData || [], val?.yData || []))
  })
}
initFun(props.modelValue)
</script>

<template>
  <div :style="{ width: width, height: height }" class="my-charts-dom">
    <div ref="myChartsDom" class="my-line-charts" />
  </div>
</template>

<style lang="scss">
.my-line-charts {
  >div{
    max-width: 100%;
    canvas{
      max-width: 100%;
    }
  }
}
</style>
<style lang='scss' scoped>
.my-line-charts {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  >div{
    max-width: 100%;
  }
}
.my-charts-dom {
  position: relative;
}
</style>
