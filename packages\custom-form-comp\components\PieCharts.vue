<script setup lang='ts'>
defineOptions({ name: '<PERSON><PERSON><PERSON><PERSON>' })
import * as echarts from 'echarts'
const props = withDefaults(
  defineProps<{
    width?: string
    height?: string
    chartName?: string
    modelValue: any
    title?: any
    legend?: any
    tooltip?: any,
    series?: any
  }>(),
  {
    width: '100%',
    height: '100%',
    chartName: 'myChart',
    title: {
      text: '饼图',
      show: false
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      show: false
    },
  }
)

const getOptions = (series): any => {
  return {
    title: props.title,
    tooltip: props.tooltip,
    legend: props.legend,
    series: series,
  }
}
watch(
  () => props.modelValue,
  (val: any) => {
    initFun(val)
  }
)

const initFun = (val: any) => {
  nextTick(() => {
    const myChartsDom = document.getElementById(props.chartName)
    const myChart = echarts.init(myChartsDom)
    myChart.setOption(getOptions(val))
  })
}
initFun(props.modelValue)
</script>

<template>
  <div :style="{ width: width, height: height }" class="my-charts-dom">
    <div :id="chartName" class="my-charts" />
  </div>
</template>

<style lang="scss">
.my-charts {
  >div{
    max-width: 100%;
    canvas{
      max-width: 100%;
    }
  }
}
</style>
<style lang='scss' scoped>
.my-charts {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  >div{
    max-width: 100%;
  }
}
.my-charts-dom {
  position: relative;
}
</style>
