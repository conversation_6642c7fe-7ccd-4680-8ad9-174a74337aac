const customFormComp = [
  {
    name: '上传文件按钮',
    el: 'button-uploader',
    isFormFetch: true,
    options: {
      defaultValue: [],
      labelWidth: 0,
      labelPosition: null,
      customClass: '',
      linkFieldData: null,
      isLabelWidth: false,
      hidden: false,
      dataBind: true,
      validator: '',
      extendProps: {},
      extendAttrs: {
        params: {},
        accept: 'image/png, image/gif, image/jpg, image/jpeg',
        btnName: '上传文件',
        url: ''
      }
    },
    events: {
      triggerFun: ''
    }
  },
  {
    name: '风险点识别',
    el: 'risk-point-identify',
    isFormFetch: true,
    options: {
      defaultValue: [],
      labelWidth: 0,
      labelPosition: null,
      customClass: '',
      linkFieldData: null,
      isLabelWidth: false,
      hidden: false,
      dataBind: true,
      validator: '',
      extendProps: {},
      extendAttrs: {
        params: {},
        accept: 'image/png, image/gif, image/jpg, image/jpeg',
        btnName: '上传文件',
        url: ''
      }
    },
    events: {
      triggerFun: ''
    }
  },
  {
    name: '饼图',
    el: 'pie-charts',
    isFormFetch: true,
    options: {
      defaultValue: [
        {
          name: '',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 1048, name: 'Search Engine' },
            { value: 735, name: 'Direct' },
            { value: 580, name: 'Email' },
            { value: 484, name: 'Union Ads' },
            { value: 300, name: 'Video Ads' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ],
      labelWidth: 0,
      labelPosition: null,
      flowableVar: true,
      customClass: '',
      width: '100%',
      height: '100%',
      isLabelWidth: false,
      hidden: false, // 隐藏属性
      dataBind: true, // 数据绑定属性
      extendProps: {},
      extendAttrs: {
        chartName: 'myChart',
        title: {
          text: '饼图',
          show: false
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          show: false
        },
      }
    },
    events: {}
  },
  {
    name: '折线图',
    el: 'line-charts',
    isFormFetch: true,
    options: {
      defaultValue: {
        xData: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        yData: [{
          name: '',
          value: [120, 132, 101, 134, 90, 230, 210]
        }, {
          name: '',
          value: [120, 132, 101, 134, 90, 230, 210]
        }]
      },
      labelWidth: 0,
      labelPosition: null,
      flowableVar: true,
      customClass: '',
      width: '100%',
      height: '100%',
      isLabelWidth: false,
      hidden: false, // 隐藏属性
      dataBind: true, // 数据绑定属性
      extendProps: {
      }
    },
    events: {}
  },
  {
    name: '柱状图',
    el: 'bar-charts',
    isFormFetch: true,
    options: {
      defaultValue: {
        xData: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        yData: [{
          name: '',
          value: [120, 132, 101, 134, 90, 230, 210]
        }, {
          name: '',
          value: [120, 132, 101, 134, 90, 230, 210]
        }, {
          name: '',
          value: [120, 132, 101, 134, 90, 230, 210]
        }]
      },
      labelWidth: 0,
      labelPosition: null,
      flowableVar: true,
      customClass: '',
      width: '100%',
      height: '100%',
      isLabelWidth: false,
      hidden: false, // 隐藏属性
      dataBind: true, // 数据绑定属性
      extendProps: {
      }
    },
    events: {}
  },
  {
    name: '预览带弹框',
    el: 'file-preview-dialog',
    isFormFetch: true,
    options: {
      defaultValue: [],
      labelWidth: 0,
      labelPosition: null,
      customClass: '',
      linkFieldData: null,
      isLabelWidth: false,
      hidden: false,
      dataBind: true,
      validator: '',
      extendProps: {},
      extendAttrs: {
        docId: '',
        ext: '',
        previewFileUrl: 'document/previewFile'
      }
    }
  },
]

export default customFormComp
